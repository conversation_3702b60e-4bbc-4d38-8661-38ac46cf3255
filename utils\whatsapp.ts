/**
 * WhatsApp Integration Utilities for Tera Works Photographer Booking System
 * Generates WhatsApp URLs with pre-filled messages for booking confirmations
 */

export interface BookingData {
  eventType: string;
  brideAName: string;
  groomName: string;
  email: string;
  phoneNumber: string;
  eventDate?: string;
  venue?: string;
  package?: string;
  additionalNotes?: string;
  weddingDate?: string;
  homecomingDate?: string;
  homecomingVenue?: string;
}

/**
 * Generates a WhatsApp URL with pre-filled booking confirmation message
 */
export function generateWhatsAppURL(bookingData: BookingData, bookingReference: string): string {
  const phoneNumber = '+94715768552'; // Tera Works contact number
  
  let message = `🎉 *New Booking Confirmation* 🎉\n\n`;
  message += `📋 *Booking Reference:* ${bookingReference}\n\n`;
  message += `👰 *Bride:* ${bookingData.brideAName}\n`;
  message += `🤵 *Groom:* ${bookingData.groomName}\n`;
  message += `📧 *Email:* ${bookingData.email}\n`;
  message += `📱 *Phone:* ${bookingData.phoneNumber}\n\n`;
  
  // Event type specific details
  if (bookingData.eventType === 'engagement') {
    message += `💍 *Event Type:* Engagement Photography\n`;
    if (bookingData.eventDate) message += `📅 *Date:* ${bookingData.eventDate}\n`;
    if (bookingData.venue) message += `📍 *Venue:* ${bookingData.venue}\n`;
  } else if (bookingData.eventType === 'wedding') {
    message += `💒 *Event Type:* Wedding Photography\n`;
    if (bookingData.eventDate) message += `📅 *Wedding Date:* ${bookingData.eventDate}\n`;
    if (bookingData.venue) message += `📍 *Venue:* ${bookingData.venue}\n`;
  } else if (bookingData.eventType === 'homecoming') {
    message += `🏠 *Event Type:* Homecoming Photography\n`;
    if (bookingData.eventDate) message += `📅 *Date:* ${bookingData.eventDate}\n`;
    if (bookingData.venue) message += `📍 *Venue:* ${bookingData.venue}\n`;
  } else if (bookingData.eventType === 'wedding-homecoming') {
    message += `💒🏠 *Event Type:* Wedding + Homecoming Photography\n`;
    if (bookingData.weddingDate) message += `📅 *Wedding Date:* ${bookingData.weddingDate}\n`;
    if (bookingData.homecomingDate) message += `📅 *Homecoming Date:* ${bookingData.homecomingDate}\n`;
    if (bookingData.venue) message += `📍 *Wedding Venue:* ${bookingData.venue}\n`;
    if (bookingData.homecomingVenue) message += `📍 *Homecoming Venue:* ${bookingData.homecomingVenue}\n`;
  } else if (bookingData.eventType === 'triple-combo') {
    message += `💍💒🏠 *Event Type:* Engagement + Wedding + Homecoming Photography\n`;
    if (bookingData.eventDate) message += `📅 *Engagement Date:* ${bookingData.eventDate}\n`;
    if (bookingData.weddingDate) message += `📅 *Wedding Date:* ${bookingData.weddingDate}\n`;
    if (bookingData.homecomingDate) message += `📅 *Homecoming Date:* ${bookingData.homecomingDate}\n`;
  }
  
  if (bookingData.package) {
    message += `📦 *Package:* ${bookingData.package}\n`;
  }
  
  if (bookingData.additionalNotes) {
    message += `\n📝 *Additional Notes:*\n${bookingData.additionalNotes}\n`;
  }
  
  message += `\n✨ *Thank you for choosing Tera Works!*\n`;
  message += `We'll contact you soon to confirm the details.\n\n`;
  message += `🌐 Visit: chalakadulangaphotography.com`;
  
  // Encode the message for URL
  const encodedMessage = encodeURIComponent(message);
  
  return `https://wa.me/${phoneNumber.replace('+', '')}?text=${encodedMessage}`;
}

/**
 * Generates a unique booking reference
 */
export function generateBookingReference(): string {
  const prefix = 'TW'; // Tera Works prefix
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
}

/**
 * Formats phone number for WhatsApp (removes spaces, dashes, etc.)
 */
export function formatPhoneForWhatsApp(phone: string): string {
  return phone.replace(/\D/g, '');
}

/**
 * Validates if a phone number is valid for WhatsApp
 */
export function isValidWhatsAppNumber(phone: string): boolean {
  const cleaned = formatPhoneForWhatsApp(phone);
  return cleaned.length >= 10 && cleaned.length <= 15;
}

/**
 * Generates a client WhatsApp URL for booking confirmation
 */
export function generateClientWhatsAppURL(bookingData: BookingData, bookingReference: string): string {
  const clientPhone = formatPhoneForWhatsApp(bookingData.phoneNumber);
  
  let message = `🎉 *Booking Confirmed!* 🎉\n\n`;
  message += `Hi ${bookingData.brideAName} & ${bookingData.groomName}!\n\n`;
  message += `Your booking has been confirmed with Tera Works Photography.\n\n`;
  message += `📋 *Booking Reference:* ${bookingReference}\n`;
  message += `📧 *Confirmation sent to:* ${bookingData.email}\n\n`;
  message += `We'll be in touch soon to discuss the details!\n\n`;
  message += `✨ *Tera Works - Let's Grow Together* ✨`;
  
  const encodedMessage = encodeURIComponent(message);
  
  return `https://wa.me/${clientPhone}?text=${encodedMessage}`;
}
