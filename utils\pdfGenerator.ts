/**
 * PDF Generation Utilities for Tera Works Photographer Booking System
 * Generates booking summary PDFs for client records
 */

import { BookingData } from './whatsapp';

export interface PDFBookingData extends BookingData {
  bookingReference: string;
  bookingDate: string;
  totalAmount?: number;
  paymentStatus?: string;
}

/**
 * Generates a booking summary PDF (placeholder implementation)
 * In a real implementation, this would use a PDF library like jsPDF or PDFKit
 */
export async function generateBookingSummaryPDF(bookingData: PDFBookingData): Promise<Blob> {
  // This is a placeholder implementation
  // In production, you would use a proper PDF generation library
  
  const pdfContent = generatePDFContent(bookingData);
  
  // Create a simple text file as placeholder
  const blob = new Blob([pdfContent], { type: 'text/plain' });
  
  return blob;
}

/**
 * Generates the content for the PDF
 */
function generatePDFContent(bookingData: PDFBookingData): string {
  let content = `TERA WORKS PHOTOGRAPHY\nBOOKING SUMMARY\n\n`;
  content += `${'='.repeat(50)}\n\n`;
  
  content += `Booking Reference: ${bookingData.bookingReference}\n`;
  content += `Booking Date: ${bookingData.bookingDate}\n`;
  content += `Event Type: ${bookingData.eventType}\n\n`;
  
  content += `CLIENT DETAILS:\n`;
  content += `Bride: ${bookingData.brideAName}\n`;
  content += `Groom: ${bookingData.groomName}\n`;
  content += `Email: ${bookingData.email}\n`;
  content += `Phone: ${bookingData.phoneNumber}\n\n`;
  
  if (bookingData.eventType === 'engagement') {
    content += `ENGAGEMENT DETAILS:\n`;
    if (bookingData.eventDate) content += `Date: ${bookingData.eventDate}\n`;
    if (bookingData.venue) content += `Venue: ${bookingData.venue}\n`;
  } else if (bookingData.eventType === 'wedding') {
    content += `WEDDING DETAILS:\n`;
    if (bookingData.eventDate) content += `Wedding Date: ${bookingData.eventDate}\n`;
    if (bookingData.venue) content += `Venue: ${bookingData.venue}\n`;
  } else if (bookingData.eventType === 'homecoming') {
    content += `HOMECOMING DETAILS:\n`;
    if (bookingData.eventDate) content += `Date: ${bookingData.eventDate}\n`;
    if (bookingData.venue) content += `Venue: ${bookingData.venue}\n`;
  } else if (bookingData.eventType === 'wedding-homecoming') {
    content += `WEDDING + HOMECOMING DETAILS:\n`;
    if (bookingData.weddingDate) content += `Wedding Date: ${bookingData.weddingDate}\n`;
    if (bookingData.homecomingDate) content += `Homecoming Date: ${bookingData.homecomingDate}\n`;
    if (bookingData.venue) content += `Wedding Venue: ${bookingData.venue}\n`;
    if (bookingData.homecomingVenue) content += `Homecoming Venue: ${bookingData.homecomingVenue}\n`;
  } else if (bookingData.eventType === 'triple-combo') {
    content += `ENGAGEMENT + WEDDING + HOMECOMING DETAILS:\n`;
    if (bookingData.eventDate) content += `Engagement Date: ${bookingData.eventDate}\n`;
    if (bookingData.weddingDate) content += `Wedding Date: ${bookingData.weddingDate}\n`;
    if (bookingData.homecomingDate) content += `Homecoming Date: ${bookingData.homecomingDate}\n`;
  }
  
  content += `\n`;
  
  if (bookingData.package) {
    content += `Package: ${bookingData.package}\n`;
  }
  
  if (bookingData.totalAmount) {
    content += `Total Amount: Rs. ${bookingData.totalAmount.toLocaleString()}\n`;
  }
  
  if (bookingData.paymentStatus) {
    content += `Payment Status: ${bookingData.paymentStatus}\n`;
  }
  
  if (bookingData.additionalNotes) {
    content += `\nAdditional Notes:\n${bookingData.additionalNotes}\n`;
  }
  
  content += `\n${'='.repeat(50)}\n`;
  content += `Thank you for choosing Tera Works Photography!\n`;
  content += `Contact: +94 71 576 8552\n`;
  content += `Website: chalakadulangaphotography.com\n`;
  content += `"Let's Grow Together"\n`;
  
  return content;
}

/**
 * Downloads the PDF file
 */
export function downloadPDF(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Generates a filename for the booking PDF
 */
export function generatePDFFilename(bookingData: PDFBookingData): string {
  const date = new Date().toISOString().split('T')[0];
  const names = `${bookingData.brideAName}_${bookingData.groomName}`.replace(/\s+/g, '_');
  return `TeraWorks_Booking_${bookingData.bookingReference}_${names}_${date}.txt`;
}

/**
 * Generates and downloads a booking summary PDF
 */
export async function generateAndDownloadBookingPDF(bookingData: PDFBookingData): Promise<void> {
  try {
    const pdfBlob = await generateBookingSummaryPDF(bookingData);
    const filename = generatePDFFilename(bookingData);
    downloadPDF(pdfBlob, filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate booking summary PDF');
  }
}
